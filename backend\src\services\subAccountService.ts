import logger from '../utils/logger';
import prisma from '../config/database';
import systemManagementService from './systemManagementService';
import { generateBothPasswords } from '../utils/passwordUtils';
import { encrypt } from '../utils/encryption';
import crypto from 'crypto';

/**
 * 处理BigInt序列化问题的工具函数
 */
function serializeBigInt(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'bigint') {
    return Number(obj);
  }

  if (Array.isArray(obj)) {
    return obj.map(serializeBigInt);
  }

  if (typeof obj === 'object') {
    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = serializeBigInt(value);
    }
    return result;
  }

  return obj;
}

interface SubAccountPermissions {
  emailSend: boolean;
  emailReceive: boolean;
  emailDelete: boolean;
  folderManage: boolean;
  contactManage: boolean;
  templateManage: boolean;
  ruleManage: boolean;
}

interface CreateSubAccountData {
  email: string;
  username: string;
  displayName?: string;
  permissions: SubAccountPermissions;
  quotaLimit?: number;
  password?: string;
}

interface RandomGenerationConfig {
  usernamePrefix?: string;
  usernameSuffix?: string;
  usernameLength?: number;
  emailDomain?: string;
  includeNumbers?: boolean;
  includeSpecialChars?: boolean;
}

interface SubAccountInviteData {
  email: string;
  permissions: SubAccountPermissions;
  quotaLimit?: number;
  expiresInHours?: number;
}

class SubAccountService {

  /**
   * 检查用户是否可以创建子账户
   */
  async canCreateSubAccount(parentUserId: number): Promise<{
    canCreate: boolean;
    reason?: string;
    currentCount: number;
    maxAllowed: number;
  }> {
    try {
      const parentUser = await prisma.user.findUnique({
        where: { id: parentUserId },
        include: {
          subAccounts: {
            where: { isActive: true }
          }
        }
      });

      if (!parentUser) {
        return {
          canCreate: false,
          reason: '父账户不存在',
          currentCount: 0,
          maxAllowed: 0
        };
      }

      if (!parentUser.isSubAccountEnabled) {
        return {
          canCreate: false,
          reason: '子账户功能未启用',
          currentCount: 0,
          maxAllowed: parentUser.maxSubAccounts
        };
      }

      const currentCount = parentUser.subAccounts.length;
      const canCreate = currentCount < parentUser.maxSubAccounts;

      return {
        canCreate,
        reason: canCreate ? undefined : '已达到最大子账户数量限制',
        currentCount,
        maxAllowed: parentUser.maxSubAccounts
      };

    } catch (error) {
      logger.error('检查子账户创建权限失败:', error);
      return {
        canCreate: false,
        reason: '检查权限失败',
        currentCount: 0,
        maxAllowed: 0
      };
    }
  }

  /**
   * 创建子账户
   */
  async createSubAccount(parentUserId: number, data: CreateSubAccountData): Promise<any> {
    try {
      // 检查是否可以创建子账户
      const canCreate = await this.canCreateSubAccount(parentUserId);
      if (!canCreate.canCreate) {
        throw new Error(canCreate.reason || '无法创建子账户');
      }

      // 检查邮箱和用户名是否已存在
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { email: data.email },
            { username: data.username }
          ]
        }
      });

      if (existingUser) {
        throw new Error('邮箱或用户名已存在');
      }

      // 获取父账户信息
      const parentUser = await prisma.user.findUnique({
        where: { id: parentUserId }
      });

      if (!parentUser) {
        throw new Error('父账户不存在');
      }

      // 生成密码
      const password = data.password || this.generateRandomPassword();
      const { webPassword, mailPassword } = await generateBothPasswords(password);

      // 处理配额，确保是BigInt类型
      const quotaValue = data.quotaLimit
        ? BigInt(data.quotaLimit * 1024 * 1024) // 转换MB到字节
        : parentUser.subAccountQuota || BigInt(**********); // 1GB默认

      // 创建子账户
      const subAccount = await prisma.user.create({
        data: {
          email: data.email,
          username: data.username,
          displayName: data.displayName,
          password: webPassword,
          mailPassword,
          parentUserId,
          accountType: 'sub',
          domainId: parentUser.domainId,
          quota: quotaValue,
          isActive: true,
          emailVerified: true,
          role: 'user'
        }
      });

      // 设置权限
      await this.setSubAccountPermissions(subAccount.id, data.permissions);

      // 初始化使用量统计
      await this.initializeUsageStats(subAccount.id, data.quotaLimit);

      // 创建默认文件夹
      await this.createDefaultFolders(subAccount.id);

      // 创建默认邮箱账户
      await this.createDefaultEmailAccount(subAccount.id, data.email, subAccount);

      // 记录活动日志
      await this.logSubAccountActivity(
        parentUserId,
        subAccount.id,
        'account_created',
        `创建子账户: ${data.email}`,
        { permissions: data.permissions, quotaLimit: data.quotaLimit }
      );

      // 记录系统事件
      await systemManagementService.recordSystemException(
        'info',
        '子账户创建',
        `用户 ${parentUser.email} 创建了子账户 ${data.email}`,
        'sub_account_management',
        { parentUserId, subAccountId: subAccount.id, email: data.email }
      );

      logger.info(`子账户创建成功: ${data.email} (父账户: ${parentUser.email})`);

      // 处理BigInt序列化并返回数据
      const result = serializeBigInt({
        ...subAccount,
        password: undefined, // 不返回密码
        mailPassword: undefined,
        temporaryPassword: password // 返回临时密码供首次登录
      });

      return result;

    } catch (error) {
      logger.error('创建子账户失败:', error);
      throw error;
    }
  }

  /**
   * 发送子账户邀请
   */
  async sendSubAccountInvitation(parentUserId: number, data: SubAccountInviteData): Promise<any> {
    try {
      // 检查是否可以创建子账户
      const canCreate = await this.canCreateSubAccount(parentUserId);
      if (!canCreate.canCreate) {
        throw new Error(canCreate.reason || '无法发送邀请');
      }

      // 检查邮箱是否已存在
      const existingUser = await prisma.user.findUnique({
        where: { email: data.email }
      });

      if (existingUser) {
        throw new Error('该邮箱已被使用');
      }

      // 检查是否已有未过期的邀请
      const existingInvitation = await prisma.subAccountInvitation.findFirst({
        where: {
          parentUserId,
          email: data.email,
          status: 'pending',
          expiresAt: { gt: new Date() }
        }
      });

      if (existingInvitation) {
        throw new Error('该邮箱已有未过期的邀请');
      }

      // 生成邀请令牌
      const invitationToken = crypto.randomBytes(32).toString('hex');
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + (data.expiresInHours || 72)); // 默认72小时

      // 创建邀请记录
      const invitation = await prisma.subAccountInvitation.create({
        data: {
          parentUserId,
          email: data.email,
          invitationToken,
          permissions: JSON.stringify(data.permissions),
          quotaLimit: data.quotaLimit,
          expiresAt,
          status: 'pending'
        }
      });

      // 发送邀请邮件
      await this.sendInvitationEmail(invitation);

      // 记录活动日志
      await this.logSubAccountActivity(
        parentUserId,
        0, // 还没有子账户ID
        'invitation_sent',
        `发送子账户邀请: ${data.email}`,
        { invitationId: invitation.id, expiresAt }
      );

      logger.info(`子账户邀请已发送: ${data.email} (父账户ID: ${parentUserId})`);

      return {
        id: invitation.id,
        email: data.email,
        invitationToken,
        expiresAt,
        status: 'pending'
      };

    } catch (error) {
      logger.error('发送子账户邀请失败:', error);
      throw error;
    }
  }

  /**
   * 接受子账户邀请
   */
  async acceptSubAccountInvitation(invitationToken: string, userData: {
    username: string;
    displayName?: string;
    password: string;
  }): Promise<any> {
    try {
      // 查找邀请
      const invitation = await prisma.subAccountInvitation.findUnique({
        where: { invitationToken },
        include: { parentUser: true }
      });

      if (!invitation) {
        throw new Error('邀请不存在或已失效');
      }

      if (invitation.status !== 'pending') {
        throw new Error('邀请已被处理');
      }

      if (invitation.expiresAt < new Date()) {
        // 更新邀请状态为过期
        await prisma.subAccountInvitation.update({
          where: { id: invitation.id },
          data: { status: 'expired' }
        });
        throw new Error('邀请已过期');
      }

      // 检查用户名是否已存在
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { email: invitation.email },
            { username: userData.username }
          ]
        }
      });

      if (existingUser) {
        throw new Error('邮箱或用户名已存在');
      }

      // 解析权限
      const permissions = JSON.parse(invitation.permissions || '{}');

      // 创建子账户
      const subAccount = await this.createSubAccount(invitation.parentUserId, {
        email: invitation.email,
        username: userData.username,
        displayName: userData.displayName,
        permissions,
        quotaLimit: invitation.quotaLimit ? Number(invitation.quotaLimit) / (1024 * 1024) : undefined, // 转换为MB
        password: userData.password
      });

      // 更新邀请状态
      await prisma.subAccountInvitation.update({
        where: { id: invitation.id },
        data: {
          status: 'accepted',
          acceptedAt: new Date(),
          subUserId: subAccount.id
        }
      });

      logger.info(`子账户邀请已接受: ${invitation.email}`);

      return serializeBigInt({
        ...subAccount,
        password: undefined,
        mailPassword: undefined
      });

    } catch (error) {
      logger.error('接受子账户邀请失败:', error);
      throw error;
    }
  }

  /**
   * 获取子账户列表
   */
  async getSubAccounts(parentUserId: number, options: {
    includeInactive?: boolean;
    page?: number;
    limit?: number;
  } = {}): Promise<{
    subAccounts: any[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const { includeInactive = false, page = 1, limit = 20 } = options;
      const skip = (page - 1) * limit;

      const where = {
        parentUserId,
        ...(includeInactive ? {} : { isActive: true })
      };

      const [subAccounts, total] = await Promise.all([
        prisma.user.findMany({
          where,
          include: {
            subAccountPermissions: true,
            subAccountUsage: true,
            emailAccounts: {
              select: {
                id: true,
                email: true,
                connectionStatus: true,
                syncStatus: true
              }
            },
            _count: {
              select: {
                emails: { where: { isDeleted: false } },
                folders: true,
                contacts: true
              }
            }
          },
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.user.count({ where })
      ]);

      return {
        subAccounts: subAccounts.map(account => serializeBigInt({
          ...account,
          password: undefined,
          mailPassword: undefined
        })),
        total,
        page,
        limit
      };

    } catch (error) {
      logger.error('获取子账户列表失败:', error);
      throw error;
    }
  }

  /**
   * 设置子账户权限
   */
  private async setSubAccountPermissions(subUserId: number, permissions: SubAccountPermissions): Promise<void> {
    const permissionMappings = [
      { type: 'email_send', allowed: permissions.emailSend },
      { type: 'email_receive', allowed: permissions.emailReceive },
      { type: 'email_delete', allowed: permissions.emailDelete },
      { type: 'folder_manage', allowed: permissions.folderManage },
      { type: 'contact_manage', allowed: permissions.contactManage },
      { type: 'template_manage', allowed: permissions.templateManage },
      { type: 'rule_manage', allowed: permissions.ruleManage }
    ];

    for (const perm of permissionMappings) {
      await prisma.subAccountPermission.upsert({
        where: {
          subUserId_permissionType: {
            subUserId,
            permissionType: perm.type
          }
        },
        update: { isAllowed: perm.allowed },
        create: {
          subUserId,
          permissionType: perm.type,
          isAllowed: perm.allowed
        }
      });
    }
  }

  /**
   * 初始化使用量统计
   */
  private async initializeUsageStats(subUserId: number, quotaLimit?: number): Promise<void> {
    const usageTypes = [
      { type: 'storage', limit: quotaLimit },
      { type: 'emails_sent', limit: null },
      { type: 'emails_received', limit: null },
      { type: 'contacts', limit: null },
      { type: 'folders', limit: null }
    ];

    for (const usage of usageTypes) {
      await prisma.subAccountUsage.create({
        data: {
          subUserId,
          usageType: usage.type,
          currentUsage: 0,
          usageLimit: usage.limit
        }
      });
    }
  }

  /**
   * 创建默认文件夹
   */
  private async createDefaultFolders(userId: number): Promise<void> {
    const defaultFolders = [
      { name: '收件箱', type: 'inbox' },
      { name: '已发送', type: 'sent' },
      { name: '草稿箱', type: 'draft' },
      { name: '垃圾箱', type: 'trash' }
    ];

    for (const folder of defaultFolders) {
      await prisma.folder.create({
        data: {
          userId,
          name: folder.name,
          type: folder.type,
          parentId: null
        }
      });
    }
  }

  /**
   * 创建默认邮箱账户
   */
  private async createDefaultEmailAccount(userId: number, email: string, user: any): Promise<void> {
    await prisma.emailAccount.create({
      data: {
        userId,
        name: '默认邮箱',
        email,
        displayName: user.displayName || user.username,
        imapHost: 'mail.blindedby.love',
        imapPort: 993,
        imapSecure: true,
        imapUsername: email,
        imapPassword: encrypt(user.mailPassword),
        smtpHost: 'mail.blindedby.love',
        smtpPort: 587,
        smtpSecure: false,
        smtpUsername: email,
        smtpPassword: encrypt(user.mailPassword),
        authType: 'password',
        isDefault: true,
        isActive: true,
        syncEnabled: true,
        syncInterval: 5,
        autoConfigured: true,
        provider: 'blindedby.love'
      }
    });
  }

  /**
   * 记录子账户活动
   */
  private async logSubAccountActivity(
    parentUserId: number,
    subUserId: number,
    activityType: string,
    description: string,
    metadata?: any
  ): Promise<void> {
    try {
      await prisma.subAccountActivity.create({
        data: {
          parentUserId,
          subUserId,
          activityType,
          activityDescription: description,
          metadata: metadata ? JSON.stringify(metadata) : null
        }
      });
    } catch (error) {
      logger.error('记录子账户活动失败:', error);
    }
  }

  /**
   * 发送邀请邮件
   */
  private async sendInvitationEmail(invitation: any): Promise<void> {
    // TODO: 实现邮件发送逻辑
    logger.info(`TODO: 发送邀请邮件到 ${invitation.email}`);
  }

  /**
   * 生成随机密码
   */
  private generateRandomPassword(): string {
    const length = 12;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }

  /**
   * 生成随机用户名
   */
  generateRandomUsername(config: RandomGenerationConfig = {}): string {
    const {
      usernamePrefix = 'user',
      usernameSuffix = '',
      usernameLength = 8,
      includeNumbers = true,
      includeSpecialChars = false
    } = config;

    let charset = 'abcdefghijklmnopqrstuvwxyz';
    if (includeNumbers) {
      charset += '0123456789';
    }
    if (includeSpecialChars) {
      charset += '_';
    }

    let randomPart = '';
    const randomLength = Math.max(3, usernameLength - usernamePrefix.length - usernameSuffix.length);

    for (let i = 0; i < randomLength; i++) {
      randomPart += charset.charAt(Math.floor(Math.random() * charset.length));
    }

    return `${usernamePrefix}${randomPart}${usernameSuffix}`;
  }

  /**
   * 生成随机邮箱地址
   */
  async generateRandomEmail(parentUserId: number, config: RandomGenerationConfig = {}): Promise<string> {
    // 获取父账户的域名信息
    const parentUser = await prisma.user.findUnique({
      where: { id: parentUserId },
      include: {
        domain: true
      }
    });

    if (!parentUser || !parentUser.domain) {
      throw new Error('无法获取父账户域名信息');
    }

    const {
      emailDomain = parentUser.domain.name,
      usernamePrefix = 'sub',
      includeNumbers = true,
      includeSpecialChars = false
    } = config;

    // 生成邮箱用户名部分
    const username = this.generateRandomUsername({
      usernamePrefix,
      usernameLength: 10,
      includeNumbers,
      includeSpecialChars
    });

    return `${username}@${emailDomain}`;
  }

  /**
   * 生成随机显示名称
   */
  generateRandomDisplayName(_config: RandomGenerationConfig = {}): string {
    const adjectives = ['Smart', 'Quick', 'Bright', 'Swift', 'Cool', 'Nice', 'Good', 'Fast'];
    const nouns = ['User', 'Member', 'Person', 'Account', 'Client'];

    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    const number = Math.floor(Math.random() * 1000);

    return `${adjective} ${noun} ${number}`;
  }

  /**
   * 生成完整的随机子账户信息
   */
  async generateRandomSubAccountData(
    parentUserId: number,
    config: RandomGenerationConfig = {}
  ): Promise<{
    email: string;
    username: string;
    displayName: string;
    password: string;
  }> {
    const email = await this.generateRandomEmail(parentUserId, config);
    const username = this.generateRandomUsername(config);
    const displayName = this.generateRandomDisplayName(config);
    const password = this.generateRandomPassword();

    // 检查生成的邮箱和用户名是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email },
          { username }
        ]
      }
    });

    if (existingUser) {
      // 如果存在冲突，递归重新生成
      return this.generateRandomSubAccountData(parentUserId, config);
    }

    return {
      email,
      username,
      displayName,
      password
    };
  }
}

// 创建单例实例
export const subAccountService = new SubAccountService();
export default subAccountService;
