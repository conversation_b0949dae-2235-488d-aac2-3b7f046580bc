import { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Switch,
  InputNumber,
  message,
  Popconfirm,
  Tooltip,
  Progress,
  Descriptions,
  Tabs,
  Alert,
  Divider,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  MailOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SendOutlined,
  ReloadOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons';
import api from '../config/api';
import type { ApiResponse } from '../types';
import { generateRandomUsername, generateRandomEmail, generateRandomDisplayName, generateRandomPassword, defaultRandomConfig, type RandomGenerationConfig } from '../utils/randomGenerator';
import RandomGenerationConfigModal from './RandomGenerationConfig';

const { TabPane } = Tabs;

interface SubAccount {
  id: number;
  email: string;
  username: string;
  displayName?: string;
  isActive: boolean;
  createdAt: string;
  subAccountPermissions: Array<{
    permissionType: string;
    isAllowed: boolean;
  }>;
  subAccountUsage: Array<{
    usageType: string;
    currentUsage: number;
    usageLimit?: number;
  }>;
  _count: {
    emails: number;
    folders: number;
    contacts: number;
  };
}

interface SubAccountPermissions {
  emailSend: boolean;
  emailReceive: boolean;
  emailDelete: boolean;
  folderManage: boolean;
  contactManage: boolean;
  templateManage: boolean;
  ruleManage: boolean;
}

const SubAccountManagement: React.FC = () => {
  const [subAccounts, setSubAccounts] = useState<SubAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [inviteModalVisible, setInviteModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedSubAccount, setSelectedSubAccount] = useState<SubAccount | null>(null);
  const [canCreateInfo, setCanCreateInfo] = useState<any>(null);
  const [featureEnabled, setFeatureEnabled] = useState(false);
  const [randomGenerating, setRandomGenerating] = useState(false);
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [randomConfig, setRandomConfig] = useState<RandomGenerationConfig>(defaultRandomConfig);
  const [form] = Form.useForm();
  const [inviteForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 加载子账户列表
  const loadSubAccounts = async () => {
    try {
      setLoading(true);
      const response = await api.get<ApiResponse<{
        subAccounts: SubAccount[];
        total: number;
      }>>('/sub-accounts');
      setSubAccounts(response.data.data!.subAccounts);
    } catch (error) {
      console.error('加载子账户列表失败:', error);
      message.error('加载子账户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 检查是否可以创建子账户
  const checkCanCreate = async () => {
    try {
      const response = await api.get<ApiResponse<any>>('/sub-accounts/can-create');
      setCanCreateInfo(response.data.data);
      // 根据返回的信息判断功能是否启用
      if (response.data.data.reason === '子账户功能未启用') {
        setFeatureEnabled(false);
      } else {
        setFeatureEnabled(true);
      }
    } catch (error) {
      console.error('检查创建权限失败:', error);
    }
  };

  // 启用/禁用子账户功能
  const handleToggleFeature = async (enabled: boolean) => {
    try {
      setLoading(true);
      await api.put('/sub-accounts/feature-toggle', { enabled });
      message.success(`子账户功能已${enabled ? '启用' : '禁用'}`);
      setFeatureEnabled(enabled);
      checkCanCreate();
      if (enabled) {
        loadSubAccounts();
      } else {
        setSubAccounts([]);
      }
    } catch (error: any) {
      message.error(error.response?.data?.message || '更新功能状态失败');
    } finally {
      setLoading(false);
    }
  };

  // 创建子账户
  const handleCreateSubAccount = async (values: any) => {
    try {
      setLoading(true);
      await api.post('/sub-accounts', values);
      message.success('子账户创建成功');
      setCreateModalVisible(false);
      form.resetFields();
      loadSubAccounts();
      checkCanCreate();
    } catch (error: any) {
      message.error(error.response?.data?.message || '创建子账户失败');
    } finally {
      setLoading(false);
    }
  };

  // 生成随机子账户信息
  const handleGenerateRandom = async () => {
    try {
      setRandomGenerating(true);
      const response = await api.post<ApiResponse<{
        email: string;
        username: string;
        displayName: string;
        password: string;
      }>>('/sub-accounts/generate-random', randomConfig);

      const { email, username, displayName, password } = response.data.data!;

      // 填充表单
      form.setFieldsValue({
        email,
        username,
        displayName,
        password
      });

      message.success('随机信息生成成功');
    } catch (error: any) {
      message.error(error.response?.data?.message || '生成随机信息失败');
    } finally {
      setRandomGenerating(false);
    }
  };

  // 生成单个字段的随机值
  const handleGenerateRandomField = (field: string) => {
    switch (field) {
      case 'username':
        const username = generateRandomUsername(randomConfig);
        form.setFieldValue('username', username);
        break;
      case 'email':
        const email = generateRandomEmail(randomConfig.emailDomain || 'example.com', randomConfig);
        form.setFieldValue('email', email);
        break;
      case 'displayName':
        const displayName = generateRandomDisplayName();
        form.setFieldValue('displayName', displayName);
        break;
      case 'password':
        const password = generateRandomPassword();
        form.setFieldValue('password', password);
        break;
    }
  };

  // 保存随机生成配置
  const handleSaveConfig = (config: RandomGenerationConfig) => {
    setRandomConfig(config);
    setConfigModalVisible(false);
    // 可以保存到本地存储
    localStorage.setItem('subAccountRandomConfig', JSON.stringify(config));
  };

  // 发送邀请
  const handleSendInvitation = async (values: any) => {
    try {
      setLoading(true);
      await api.post('/sub-accounts/invitations', values);
      message.success('邀请已发送');
      setInviteModalVisible(false);
      inviteForm.resetFields();
    } catch (error: any) {
      message.error(error.response?.data?.message || '发送邀请失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新权限
  const handleUpdatePermissions = async (values: any) => {
    try {
      if (!selectedSubAccount) return;
      
      setLoading(true);
      await api.put(`/sub-accounts/${selectedSubAccount.id}/permissions`, {
        permissions: values
      });
      message.success('权限更新成功');
      setEditModalVisible(false);
      editForm.resetFields();
      loadSubAccounts();
    } catch (error: any) {
      message.error(error.response?.data?.message || '更新权限失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换账户状态
  const handleToggleStatus = async (subAccount: SubAccount) => {
    try {
      await api.put(`/sub-accounts/${subAccount.id}/status`, {
        isActive: !subAccount.isActive
      });
      message.success(`子账户已${subAccount.isActive ? '停用' : '启用'}`);
      loadSubAccounts();
    } catch (error: any) {
      message.error(error.response?.data?.message || '更新状态失败');
    }
  };

  // 删除子账户
  const handleDeleteSubAccount = async (subAccount: SubAccount) => {
    try {
      await api.delete(`/sub-accounts/${subAccount.id}`);
      message.success('子账户删除成功');
      loadSubAccounts();
      checkCanCreate();
    } catch (error: any) {
      message.error(error.response?.data?.message || '删除子账户失败');
    }
  };

  // 编辑权限
  const handleEditPermissions = (subAccount: SubAccount) => {
    setSelectedSubAccount(subAccount);
    
    // 转换权限格式
    const permissions: any = {};
    subAccount.subAccountPermissions.forEach(perm => {
      const key = perm.permissionType.replace('_', '').replace(/([A-Z])/g, (match, p1, offset) => 
        offset > 0 ? match.toLowerCase() : match.toLowerCase()
      );
      permissions[key] = perm.isAllowed;
    });

    editForm.setFieldsValue(permissions);
    setEditModalVisible(true);
  };

  // 获取使用量信息
  const getUsageInfo = (subAccount: SubAccount) => {
    const storageUsage = subAccount.subAccountUsage.find(u => u.usageType === 'storage');
    if (!storageUsage) return null;

    const used = storageUsage.currentUsage;
    const limit = storageUsage.usageLimit;
    const percentage = limit ? Math.round((used / limit) * 100) : 0;

    return {
      used: Math.round(used / (1024 * 1024)), // MB
      limit: limit ? Math.round(limit / (1024 * 1024)) : null, // MB
      percentage
    };
  };

  useEffect(() => {
    loadSubAccounts();
    checkCanCreate();

    // 从本地存储加载随机生成配置
    const savedConfig = localStorage.getItem('subAccountRandomConfig');
    if (savedConfig) {
      try {
        const config = JSON.parse(savedConfig);
        setRandomConfig({ ...defaultRandomConfig, ...config });
      } catch (error) {
        console.error('加载随机生成配置失败:', error);
      }
    }
  }, []);

  const columns = [
    {
      title: '用户信息',
      key: 'userInfo',
      render: (_: any, record: SubAccount) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.displayName || record.username}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>{record.email}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'status',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'} icon={isActive ? <CheckCircleOutlined /> : <CloseCircleOutlined />}>
          {isActive ? '活跃' : '停用'}
        </Tag>
      ),
    },
    {
      title: '存储使用',
      key: 'storage',
      render: (_: any, record: SubAccount) => {
        const usage = getUsageInfo(record);
        if (!usage) return '-';
        
        return (
          <div style={{ width: 120 }}>
            <div style={{ fontSize: '12px', marginBottom: 4 }}>
              {usage.used}MB {usage.limit ? `/ ${usage.limit}MB` : ''}
            </div>
            {usage.limit && (
              <Progress 
                percent={usage.percentage} 
                size="small" 
                status={usage.percentage > 90 ? 'exception' : 'normal'}
              />
            )}
          </div>
        );
      },
    },
    {
      title: '邮件/文件夹/联系人',
      key: 'counts',
      render: (_: any, record: SubAccount) => (
        <div style={{ fontSize: '12px' }}>
          <div>邮件: {record._count.emails}</div>
          <div>文件夹: {record._count.folders}</div>
          <div>联系人: {record._count.contacts}</div>
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: SubAccount) => (
        <Space>
          <Tooltip title="编辑权限">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditPermissions(record)}
            />
          </Tooltip>
          <Tooltip title={record.isActive ? '停用' : '启用'}>
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => handleToggleStatus(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个子账户吗？"
            description="删除后将无法恢复，所有相关数据都会被删除。"
            onConfirm={() => handleDeleteSubAccount(record)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        title="子账户管理"
        extra={
          <Space>
            {!featureEnabled ? (
              <Button
                type="primary"
                onClick={() => handleToggleFeature(true)}
                loading={loading}
              >
                启用子账户功能
              </Button>
            ) : (
              <>
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={() => setInviteModalVisible(true)}
                  disabled={!canCreateInfo?.canCreate}
                >
                  发送邀请
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setCreateModalVisible(true)}
                  disabled={!canCreateInfo?.canCreate}
                >
                  创建子账户
                </Button>
                <Button
                  danger
                  onClick={() => handleToggleFeature(false)}
                  loading={loading}
                >
                  禁用功能
                </Button>
              </>
            )}
          </Space>
        }
      >
        {!featureEnabled ? (
          <Alert
            message="子账户功能未启用"
            description="启用子账户功能后，您可以创建和管理子账户，为不同的用途分配独立的邮箱地址。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        ) : (
          <>
            {/* 创建限制提示 */}
            {canCreateInfo && (
              <Alert
                message={
                  <div>
                    <span>子账户使用情况: {canCreateInfo.currentCount}/{canCreateInfo.maxAllowed}</span>
                    {!canCreateInfo.canCreate && (
                      <span style={{ marginLeft: 16, color: '#f5222d' }}>
                        {canCreateInfo.reason}
                      </span>
                    )}
                  </div>
                }
                type={canCreateInfo.canCreate ? 'info' : 'warning'}
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}

            <Table
              columns={columns}
              dataSource={subAccounts}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个子账户`,
              }}
            />
          </>
        )}
      </Card>

      {/* 创建子账户模态框 */}
      <Modal
        title="创建子账户"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateSubAccount}
        >
          {/* 随机生成按钮 */}
          <div style={{ marginBottom: 16, textAlign: 'center' }}>
            <Space>
              <Button
                type="dashed"
                icon={<ThunderboltOutlined />}
                onClick={handleGenerateRandom}
                loading={randomGenerating}
              >
                一键随机生成
              </Button>
              <Button
                icon={<SettingOutlined />}
                onClick={() => setConfigModalVisible(true)}
                title="配置随机生成规则"
              >
                配置
              </Button>
            </Space>
            <div style={{ color: '#666', fontSize: '12px', marginTop: 8 }}>
              或手动填写以下信息
            </div>
          </div>

          <Form.Item
            label={
              <Space>
                邮箱地址
                <Button
                  type="link"
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={() => handleGenerateRandomField('email')}
                  style={{ padding: 0, height: 'auto' }}
                >
                  随机
                </Button>
              </Space>
            }
            name="email"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input prefix={<MailOutlined />} placeholder="<EMAIL>" />
          </Form.Item>

          <Form.Item
            label={
              <Space>
                用户名
                <Button
                  type="link"
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={() => handleGenerateRandomField('username')}
                  style={{ padding: 0, height: 'auto' }}
                >
                  随机
                </Button>
              </Space>
            }
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { pattern: /^[a-zA-Z0-9_]{3,20}$/, message: '用户名只能包含字母、数字、下划线，长度3-20位' }
            ]}
          >
            <Input prefix={<UserOutlined />} placeholder="username" />
          </Form.Item>

          <Form.Item
            label={
              <Space>
                显示名称
                <Button
                  type="link"
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={() => handleGenerateRandomField('displayName')}
                  style={{ padding: 0, height: 'auto' }}
                >
                  随机
                </Button>
              </Space>
            }
            name="displayName"
          >
            <Input placeholder="显示名称（可选）" />
          </Form.Item>

          <Form.Item
            label={
              <Space>
                初始密码
                <Button
                  type="link"
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={() => handleGenerateRandomField('password')}
                  style={{ padding: 0, height: 'auto' }}
                >
                  随机
                </Button>
              </Space>
            }
            name="password"
            extra="留空将自动生成密码"
          >
            <Input.Password placeholder="留空自动生成" />
          </Form.Item>

          <Divider>权限设置</Divider>

          <Form.Item label="邮件权限">
            <Space direction="vertical">
              <Form.Item name={['permissions', 'emailSend']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>发送邮件</span>
              </Form.Item>
              <Form.Item name={['permissions', 'emailReceive']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>接收邮件</span>
              </Form.Item>
              <Form.Item name={['permissions', 'emailDelete']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>删除邮件</span>
              </Form.Item>
            </Space>
          </Form.Item>

          <Form.Item label="管理权限">
            <Space direction="vertical">
              <Form.Item name={['permissions', 'folderManage']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>管理文件夹</span>
              </Form.Item>
              <Form.Item name={['permissions', 'contactManage']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>管理联系人</span>
              </Form.Item>
              <Form.Item name={['permissions', 'templateManage']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>管理模板</span>
              </Form.Item>
              <Form.Item name={['permissions', 'ruleManage']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>管理规则</span>
              </Form.Item>
            </Space>
          </Form.Item>

          <Form.Item
            label="存储配额 (MB)"
            name="quotaLimit"
            extra="留空表示无限制"
          >
            <InputNumber
              min={1}
              max={10240}
              placeholder="1024"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                创建子账户
              </Button>
              <Button onClick={() => setCreateModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 发送邀请模态框 */}
      <Modal
        title="发送子账户邀请"
        open={inviteModalVisible}
        onCancel={() => setInviteModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={inviteForm}
          layout="vertical"
          onFinish={handleSendInvitation}
          initialValues={{
            expiresInHours: 72,
            permissions: {
              emailSend: true,
              emailReceive: true,
              emailDelete: false,
              folderManage: true,
              contactManage: true,
              templateManage: false,
              ruleManage: false
            }
          }}
        >
          <Form.Item
            label="邀请邮箱"
            name="email"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input prefix={<MailOutlined />} placeholder="<EMAIL>" />
          </Form.Item>

          <Form.Item
            label="邀请有效期 (小时)"
            name="expiresInHours"
            rules={[{ required: true, message: '请设置有效期' }]}
          >
            <InputNumber min={1} max={168} style={{ width: '100%' }} />
          </Form.Item>

          <Divider>权限设置</Divider>

          <Form.Item label="邮件权限">
            <Space direction="vertical">
              <Form.Item name={['permissions', 'emailSend']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>发送邮件</span>
              </Form.Item>
              <Form.Item name={['permissions', 'emailReceive']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>接收邮件</span>
              </Form.Item>
              <Form.Item name={['permissions', 'emailDelete']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>删除邮件</span>
              </Form.Item>
            </Space>
          </Form.Item>

          <Form.Item label="管理权限">
            <Space direction="vertical">
              <Form.Item name={['permissions', 'folderManage']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>管理文件夹</span>
              </Form.Item>
              <Form.Item name={['permissions', 'contactManage']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>管理联系人</span>
              </Form.Item>
              <Form.Item name={['permissions', 'templateManage']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>管理模板</span>
              </Form.Item>
              <Form.Item name={['permissions', 'ruleManage']} valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>管理规则</span>
              </Form.Item>
            </Space>
          </Form.Item>

          <Form.Item
            label="存储配额 (MB)"
            name="quotaLimit"
            extra="留空表示无限制"
          >
            <InputNumber
              min={1}
              max={10240}
              placeholder="1024"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                发送邀请
              </Button>
              <Button onClick={() => setInviteModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑权限模态框 */}
      <Modal
        title={`编辑权限 - ${selectedSubAccount?.displayName || selectedSubAccount?.username}`}
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleUpdatePermissions}
        >
          <Form.Item label="邮件权限">
            <Space direction="vertical">
              <Form.Item name="emailsend" valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>发送邮件</span>
              </Form.Item>
              <Form.Item name="emailreceive" valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>接收邮件</span>
              </Form.Item>
              <Form.Item name="emaildelete" valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>删除邮件</span>
              </Form.Item>
            </Space>
          </Form.Item>

          <Form.Item label="管理权限">
            <Space direction="vertical">
              <Form.Item name="foldermanage" valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>管理文件夹</span>
              </Form.Item>
              <Form.Item name="contactmanage" valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>管理联系人</span>
              </Form.Item>
              <Form.Item name="templatemanage" valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>管理模板</span>
              </Form.Item>
              <Form.Item name="rulemanage" valuePropName="checked" noStyle>
                <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                <span style={{ marginLeft: 8 }}>管理规则</span>
              </Form.Item>
            </Space>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                更新权限
              </Button>
              <Button onClick={() => setEditModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 随机生成配置模态框 */}
      <RandomGenerationConfigModal
        visible={configModalVisible}
        onCancel={() => setConfigModalVisible(false)}
        onSave={handleSaveConfig}
        initialConfig={randomConfig}
      />
    </div>
  );
};

export default SubAccountManagement;
