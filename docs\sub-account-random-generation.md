# 子账户随机生成功能

## 功能概述

主子账户创建功能现在支持随机生成子账户信息，包括用户名、邮箱地址、显示名称和密码。用户可以通过一键随机生成或单独生成各个字段，并可以配置生成规则。

## 功能特性

### 1. 一键随机生成
- 点击"一键随机生成"按钮可以同时生成所有必要的子账户信息
- 生成的信息会自动填充到表单中
- 支持后端API调用，确保生成的邮箱和用户名不会与现有账户冲突

### 2. 单字段随机生成
- 每个输入字段旁边都有"随机"按钮
- 可以单独生成用户名、邮箱、显示名称或密码
- 使用前端工具函数，响应速度快

### 3. 可配置的生成规则
- 点击"配置"按钮可以打开配置面板
- 支持配置用户名前缀、后缀、长度
- 支持配置邮箱域名
- 支持选择是否包含数字和特殊字符
- 配置会保存到本地存储，下次使用时自动加载

## 使用方法

### 基本使用

1. 在子账户管理页面点击"创建子账户"
2. 在弹出的表单中，可以看到"一键随机生成"和"配置"按钮
3. 点击"一键随机生成"自动填充所有字段
4. 或者点击各字段旁边的"随机"按钮单独生成

### 配置生成规则

1. 点击"配置"按钮打开配置面板
2. 在"用户名生成规则"部分：
   - 设置用户名前缀（默认：sub）
   - 设置用户名后缀（可选）
   - 设置用户名总长度（3-20位）
   - 选择是否包含数字和特殊字符
3. 在"邮箱生成规则"部分：
   - 设置邮箱域名（默认使用父账户域名）
4. 点击"保存配置"应用设置

### 配置示例

**默认配置：**
```json
{
  "usernamePrefix": "sub",
  "usernameSuffix": "",
  "usernameLength": 8,
  "emailDomain": "example.com",
  "includeNumbers": true,
  "includeSpecialChars": false
}
```

**自定义配置示例：**
```json
{
  "usernamePrefix": "team",
  "usernameSuffix": "_user",
  "usernameLength": 12,
  "emailDomain": "company.com",
  "includeNumbers": true,
  "includeSpecialChars": true
}
```

## 生成规则说明

### 用户名生成
- 格式：`{前缀}{随机字符}{后缀}`
- 随机字符包含小写字母，可选数字和下划线
- 总长度在3-20位之间
- 自动检查唯一性，避免冲突

### 邮箱生成
- 格式：`{用户名部分}@{域名}`
- 用户名部分使用配置的规则生成
- 域名可以自定义或使用父账户域名
- 自动检查唯一性，避免冲突

### 显示名称生成
- 格式：`{形容词} {名词} {数字}`
- 从预定义的形容词和名词列表中随机选择
- 数字范围：0-999

### 密码生成
- 长度：12位
- 包含大小写字母、数字和特殊字符
- 确保包含各种字符类型以提高安全性

## API接口

### 生成随机子账户信息
```
POST /api/sub-accounts/generate-random
```

**请求体：**
```json
{
  "usernamePrefix": "sub",
  "usernameSuffix": "",
  "usernameLength": 8,
  "emailDomain": "example.com",
  "includeNumbers": true,
  "includeSpecialChars": false
}
```

**响应：**
```json
{
  "success": true,
  "data": {
    "email": "<EMAIL>",
    "username": "user7x9k2",
    "displayName": "Smart User 123",
    "password": "Kx9#mP2qR5tY"
  }
}
```

## 技术实现

### 后端实现
- `subAccountService.ts`：核心生成逻辑
- `subAccountController.ts`：API控制器
- 支持配置参数和冲突检测

### 前端实现
- `randomGenerator.ts`：前端工具函数
- `RandomGenerationConfig.tsx`：配置组件
- `SubAccountManagement.tsx`：主界面集成

### 数据持久化
- 配置保存到浏览器本地存储
- 支持配置的导入导出（未来功能）

## 注意事项

1. 生成的信息仅为建议，用户可以手动修改
2. 后端会进行最终的唯一性检查
3. 配置保存在本地，清除浏览器数据会丢失配置
4. 邮箱域名必须是有效的域名格式
5. 用户名长度限制为3-20位，包括前缀和后缀
